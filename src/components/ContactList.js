import React, { useEffect, useRef, useState } from "react";
import {
  View,
  StyleSheet,
  Animated,
  FlatList,
  TouchableOpacity,
  Vibration,
  Platform,
} from "react-native";
import colors from "../assets/colors";
import commonStyles from "../assets/commonStyles";
import ContactCard from "./ContactCard";
import MyText from "./MyText";
import { useNavigation } from "@react-navigation/native";

const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("");

const ContactList = ({
  contacts = [],
  showAlphabetList = true,
  mode = "",
  onSelectionChange,
  selectedContacts = [],
  setSelectedContacts,
  reverseAlphabet = false, // New prop to control alphabet order
  ListHeaderComponent = <></>,
  microsoft = false,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const sectionListRef = useRef(null);
  const selectedLetter = useRef(new Animated.Value(0)).current;

  const [currentLetter, setCurrentLetter] = useState("");
  const [activeLetter, setActiveLetter] = useState(null);
  const activeLetterAnim = useRef(new Animated.Value(1)).current;
  const navigation = useNavigation();

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();
  }, [fadeAnim]);

  // Modified toggleContactSelection to update parent's state
  const toggleContactSelection = (contact) => {
    // Generate a robust contact ID
    const contactId =
      contact.id ||
      `${contact.firstName || "unknown"}-${contact.lastName || "unknown"}-${
        contact.emails?.[0]?.address ||
        contact.phoneNumbers?.[0]?.number ||
        JSON.stringify(contact)
      }`;

    setSelectedContacts((prev) =>
      prev.includes(contactId)
        ? prev.filter((id) => id !== contactId)
        : [...prev, contactId]
    );

    // Call onSelectionChange if provided
    if (onSelectionChange) {
      const updatedSelection = selectedContacts.includes(contactId)
        ? selectedContacts.filter((id) => id !== contactId)
        : [...selectedContacts, contactId];
      onSelectionChange(updatedSelection);
    }
  };

  // --- FLATTEN CONTACTS & MAP LETTERS TO INDICES ---
  // Support both sectioned and flat contacts array
  let flatContacts = [];
  let letterToIndex = {};
  let isSectioned = false;

  if (
    Array.isArray(contacts) &&
    contacts.length > 0 &&
    typeof contacts[0] === "object" &&
    contacts[0].data &&
    contacts[0].title
  ) {
    // Sectioned
    isSectioned = true;
    let runningIndex = 0;
    contacts.forEach((section) => {
      if (section.data.length > 0) {
        letterToIndex[section.title] = runningIndex;
        // Insert a header marker
        flatContacts.push({ __header: true, title: section.title });
        runningIndex++;
        section.data.forEach((item) => {
          flatContacts.push(item);
          runningIndex++;
        });
      }
    });
  } else if (Array.isArray(contacts)) {
    // Flat array
    flatContacts = contacts;
  }
  // console.log("🚀 ~ flatContacts:", flatContacts);
  const scrollToSection = (letter) => {
    const index = letterToIndex[letter];
    if (index !== undefined && flatListRef.current) {
      flatListRef.current.scrollToIndex({ index, animated: true });
      setCurrentLetter(letter);
      selectedLetter.setValue(0);
      Animated.timing(selectedLetter, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }).start(() => {
        setTimeout(() => {
          Animated.timing(selectedLetter, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }).start();
        }, 400);
      });
    }
  };

  // --- FLATLIST REF ---
  const flatListRef = useRef(null);

  // --- RENDER ITEM (with header support) ---
  const renderItem = ({ item }) => {
    // console.log("🚀 ~ renderItem ~ item:", item);
    if (item.__header) {
      return (
        <View
          style={{
            backgroundColor: colors.white,
            paddingVertical: 6,
            paddingHorizontal: 16,
          }}
        >
          {/* <MyText semibold color={colors.primary} children={item.title} /> */}
        </View>
      );
    }
    // Generate the same contact ID
    const contactId =
      item.id ||
      `${item.firstName || "unknown"}-${item.lastName || "unknown"}-${
        item.emails?.[0]?.address ||
        item.phoneNumbers?.[0]?.number ||
        JSON.stringify(item)
      }`;
    const isSelected = selectedContacts.includes(contactId);

    return (
      <ContactCard
        name={
          `${item?.firstName || ""} ${item?.middleName || ""} ${
            item?.lastName || ""
          }`.trim() || "Unknown"
        }
        phone={
          (item?.phoneNumbers?.[0]
            ? `${
                item.phoneNumbers[0].countryCode?.startsWith("+")
                  ? item.phoneNumbers[0].countryCode
                  : `+${item.phoneNumbers[0].countryCode || ""}`
              }${item.phoneNumbers[0].number || ""}`
            : null) ||
          item?.emails?.[0]?.address ||
          "N/A"
        }
        isSelected={isSelected}
        mode={mode}
        onPress={() => {
          if (mode === "viewprofile") {
            // console.log("Contact ID:", item);
            // return;
            navigation.navigate("ContactDetailsScreen", {
              id: item._id,
            });
          } else {
            toggleContactSelection(item);
          }
        }}
        imgUrl={item?.profile_image || ""}
        microsoft={microsoft}
      />
    );
  };

  // --- FLATLIST getItemLayout ---
  const getItemLayout = (data, index) => {
    // Header rows are shorter, contact rows are 60px
    const item = flatContacts[index];
    const length = item && item.__header ? 32 : 60;
    let offset = 0;
    for (let i = 0; i < index; i++) {
      offset += flatContacts[i].__header ? 32 : 60;
    }
    return { length, offset, index };
  };

  // Use reversed alphabet if reverseAlphabet is true, otherwise normal order
  const alphabetList = reverseAlphabet ? [...alphabet].reverse() : alphabet;
  // If contacts are sorted in 'za' order, also reverse the alphabet list
  const isZA =
    isSectioned &&
    contacts &&
    contacts.length > 0 &&
    contacts[0]?.title &&
    contacts[0]?.title > contacts[contacts.length - 1]?.title;
  const effectiveAlphabetList =
    reverseAlphabet || isZA ? [...alphabet].reverse() : alphabet;

  const renderAlphabetScroller = () => (
    <View style={styles.alphabetContainer}>
      {effectiveAlphabetList.map((letter) => {
        const isActive = activeLetter === letter;

        return (
          <TouchableOpacity
            key={letter}
            onPress={() => scrollToSection(letter)}
            onPressIn={() => {
              setActiveLetter(letter);
              Animated.spring(activeLetterAnim, {
                toValue: 1.5,
                friction: 5,
                useNativeDriver: true,
              }).start();
            }}
            onPressOut={() => {
              Animated.spring(activeLetterAnim, {
                toValue: 1,
                friction: 5,
                useNativeDriver: true,
              }).start(() => setActiveLetter(null));
            }}
            style={styles.alphabetItem}
          >
            <Animated.View
              style={[
                styles.letterWrapper,
                isActive && {
                  transform: [{ scale: activeLetterAnim }],
                  backgroundColor: colors.primaryAlpha(0.1),
                  borderRadius: 13,
                  paddingHorizontal: 6,
                },
              ]}
            >
              <MyText
                tiny
                color={isActive ? colors.primary : colors.txtGray}
                bold={isActive}
                children={letter}
              />
            </Animated.View>
          </TouchableOpacity>
        );
      })}
    </View>
  );

  // --- RENDER ---
  return (
    <View style={styles.container}>
      <View style={[styles.listContainer, commonStyles?.shadow]}>
        <FlatList
          ref={flatListRef}
          data={flatContacts}
          renderItem={renderItem}
          keyExtractor={(item, index) =>
            item.__header
              ? `header-${item.title}`
              : item.id ||
                `${item.firstName || "unknown"}-${
                  item.lastName || "unknown"
                }-${index}`
          }
          style={styles.list}
          showsVerticalScrollIndicator={false}
          initialNumToRender={10}
          maxToRenderPerBatch={10}
          windowSize={5}
          removeClippedSubviews={true}
          getItemLayout={getItemLayout}
          ListHeaderComponent={ListHeaderComponent}
        />
        <Animated.View
          pointerEvents="none"
          style={[
            styles.letterIndicator,
            {
              opacity: selectedLetter,
              transform: [
                {
                  scale: selectedLetter.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0.5, 1],
                  }),
                },
              ],
            },
          ]}
        >
          <MyText h1 color={colors.white} children={currentLetter} />
        </Animated.View>
        {showAlphabetList && isSectioned && renderAlphabetScroller()}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1 },
  listContainer: {
    flex: 0.9,
    flexDirection: "row",
    borderRadius: 13,
    marginHorizontal: 10,
    backgroundColor: colors.white,
  },
  list: {
    flex: 1,
    marginBottom: 60,
  },
  alphabetContainer: {
    width: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  letterWrapper: {
    alignItems: "center",
    justifyContent: "center",
  },
  letterIndicator: {
    position: "absolute",
    top: "40%",
    left: "40%",
    width: 60,
    height: 60,
    borderRadius: 40,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  alphabetItem: {
    // paddingVertical: 0.2,
  },
});

export default ContactList;
