import React from "react";
import {
  Image,
  StyleSheet,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import MyText from "./MyText";
import colors from "../assets/colors";
import commonStyles from "../assets/commonStyles";
import FastImage from "@d11/react-native-fast-image";

const Avatar = (props) => {
  const { url, name = "Alice", container, size, microsoft } = props;

  const getInitials = () => {
    const [firstName, lastName] = name.split(" ");

    if (firstName && lastName) {
      return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    }

    return firstName.charAt(0).toUpperCase();
  };

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={props.onPress}
      style={[
        styles.container,
        container,
        commonStyles?.header?.midIcon,
        commonStyles?.shadow,
        { width: size, height: size },
      ]}
    >
      {!!url &&
        (microsoft ? (
          <Image
            source={{ uri: url }}
            resizeMode="cover"
            style={{
              width: size,
              height: size,
              borderRadius: 30,
            }}
          />
        ) : (
          <FastImage
            source={{ uri: url, priority: FastImage.priority.high }}
            resizeMode="cover"
            style={{
              width: size,
              height: size,
              borderRadius: 30,
            }}
          />
        ))}
      {!url && <MyText bold children={getInitials()} />}
    </TouchableOpacity>
  );
};

export default Avatar;

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors?.white,
    borderRadius: 30,
  },
});
