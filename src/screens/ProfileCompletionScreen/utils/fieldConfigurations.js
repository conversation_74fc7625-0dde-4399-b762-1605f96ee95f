/**
 * Field configurations for PersonalSection form
 */

export const genderOptions = [
  { label: "Male", value: "male" },
  { label: "Female", value: "female" },
  { label: "Other", value: "other" },
];

/**
 * Initial field visibility state
 */
export const initialFieldVisibility = {
  // Basic Details
  firstName: true,
  middleName: true,
  lastName: true,
  nickname: true,
  email: true,
  dateOfBirth: true,
  gender: true,
  nationality: true,
  maritalStatus: true,
  spouseName: true,

  // Home Address
  apartment: true,
  street: true,
  city: true,
  state: true,
  zipCode: true,
  country: true,

  // Other Address
  otherApartment: true,
  otherStreet: true,
  otherCity: true,
  otherState: true,
  otherZipCode: true,
  otherCountry: true,

  // Other Details
  secondaryEmail: true,
  secondaryPhone: true,
  personalWebsite: true,
  hobbies: true,
  religion: true,
  preferredContactMethod: true,
  timezone: true,

  // Emergency Contact
  emergencyContactName: true,
  emergencyContactRelationship: true,
  emergencyEmail: true,
  emergencyPhone: true,
  emergencyAddress: true,

  // Health Insurance
  policyNumber: true,
  insuranceProvider: true,
  policyPeriod: true,
  effectiveDate: true,
  expirationDate: true,
  sumInsured: true,

  // Billing Address
  billingApartment: true,
  billingStreet: true,
  billingCity: true,
  billingState: true,
  billingZipCode: true,
  billingCountry: true,

  // Card Details
  cardName: true,
  cardNumber: true,
  cardExpiry: true,
  cardCvv: true,

  // Bank Account
  accountName: true,
  bankName: true,
  accountNumber: true,
  ifscCode: true,
};

/**
 * Basic details field configurations
 */
export const basicDetailsFields = [
  {
    type: 'input',
    name: 'firstName',
    label: 'First Name*',
    disabled: true,
    required: true,
  },
  {
    type: 'input',
    name: 'middleName',
    label: 'Middle Name',
    disabled: true,
  },
  {
    type: 'input',
    name: 'lastName',
    label: 'Last Name*',
    disabled: true,
    required: true,
  },
  {
    type: 'input',
    name: 'nickname',
    label: 'Nickname*',
    required: true,
  },
  {
    type: 'input',
    name: 'email',
    label: 'Email*',
    keyboardType: 'email-address',
    disabled: true,
    required: true,
  },
];

/**
 * Address field configurations
 */
export const addressFields = [
  {
    name: 'apartment',
    label: 'Apartment/House No./Building',
  },
  {
    name: 'street',
    label: 'Street',
  },
  {
    name: 'city',
    label: 'City',
  },
  {
    name: 'state',
    label: 'State/Province',
  },
  {
    name: 'zipCode',
    label: 'ZIP/Postal Code',
  },
  {
    name: 'country',
    label: 'Country',
  },
];

/**
 * Other details field configurations
 */
export const otherDetailsFields = [
  {
    name: 'secondaryEmail',
    label: 'Secondary Email',
    keyboardType: 'email-address',
  },
  {
    name: 'personalWebsite',
    label: 'Personal Website (if any)',
    placeholder: 'https://example.com',
    keyboardType: 'url',
  },
  {
    name: 'hobbies',
    label: 'Hobbies/Interests',
  },
  {
    name: 'religion',
    label: 'Religion',
  },
  {
    name: 'preferredContactMethod',
    label: 'Preferred Contact Method',
  },
];

/**
 * Emergency contact field configurations
 */
export const emergencyContactFields = [
  {
    name: 'emergencyContactName',
    label: 'Emergency Contact Name',
  },
  {
    name: 'emergencyContactRelationship',
    label: 'Emergency Contact Relationship',
  },
  {
    name: 'emergencyEmail',
    label: 'Emergency Email',
    keyboardType: 'email-address',
  },
  {
    name: 'emergencyAddress',
    label: 'Emergency Address',
  },
];

/**
 * Health insurance field configurations
 */
export const healthInsuranceFields = [
  {
    name: 'policyNumber',
    label: 'Policy Number',
  },
  {
    name: 'insuranceProvider',
    label: 'Insurance Provider',
  },
  {
    name: 'policyPeriod',
    label: 'Policy Period (in years)',
    keyboardType: 'numeric',
  },
  {
    name: 'sumInsured',
    label: 'Sum Insured',
    keyboardType: 'numeric',
  },
];

/**
 * Card details field configurations
 */
export const cardDetailsFields = [
  {
    name: 'cardName',
    label: 'Name on Card',
    placeholder: 'Enter name as it appears on card',
  },
  {
    name: 'cardNumber',
    label: 'Card Number',
    keyboardType: 'numeric',
    maxLength: 19,
  },
  {
    name: 'cardExpiry',
    label: 'Expiry Date (MM/YY)',
    placeholder: 'MM/YY',
    keyboardType: 'numeric',
    maxLength: 5,
    width: '48%',
  },
  {
    name: 'cardCvv',
    label: 'CVV',
    keyboardType: 'numeric',
    maxLength: 4,
    width: '48%',
  },
];

/**
 * Bank account field configurations
 */
export const bankAccountFields = [
  {
    name: 'accountName',
    label: 'Account Holder Name',
    placeholder: 'Enter full name as per bank records',
  },
  {
    name: 'bankName',
    label: 'Bank Name',
  },
  {
    name: 'accountNumber',
    label: 'Bank Account Number',
    placeholder: 'Enter account number',
    keyboardType: 'numeric',
  },
  {
    name: 'ifscCode',
    label: 'IFSC Code',
    placeholder: 'e.g., SBIN0000123',
    autoCapitalize: 'characters',
    maxLength: 11,
  },
];
