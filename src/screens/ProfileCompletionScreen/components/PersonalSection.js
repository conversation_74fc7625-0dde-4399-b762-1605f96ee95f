import React, { useState } from "react";
import { View } from "react-native";
import PropTypes from "prop-types";
import MyText from "../../../components/MyText";
import CollapsibleHeader from "./CollapsibleHeader";
import FormField from "./FormField";
import AddressSection from "./AddressSection";
import AdditionalContactField from "./AdditionalContactField";
import CardDetailsSection from "./CardDetailsSection";
import BankAccountSection from "./BankAccountSection";
import { maritalStatusOptions } from "../../../utils/constants";
import { timezones } from "../../../utils/timezones";
import colors from "../../../assets/colors";
import { useFieldVisibility } from "../hooks/useFieldVisibility";
import { useCollapsibleSections } from "../hooks/useCollapsibleSections";
import {
  genderOptions,
  basicDetailsFields,
  otherDetailsFields,
  emergencyContactFields,
  healthInsuranceFields,
} from "../utils/fieldConfigurations";

const PersonalSection = React.forwardRef(
  (
    {
      form,
      handleChange,
      errors,
      phoneInputRef,
      onTimezoneDropdownToggle,
      setErrors,
      ownProfileData,
      additionalEmails = [],
      setAdditionalEmails = () => {},
      additionalPhones = [],
      setAdditionalPhones = () => {},
    },
    ref
  ) => {
    // Custom hooks for state management
    const { fieldVisibility, handleVisibilityToggle } = useFieldVisibility();

    const {
      isBasicCollapsed,
      isInsuranceCollapsed,
      isAddressCollapsed,
      isOtherAddressCollapsed,
      isBillingCollapsed,
      isCardCollapsed,
      isAccountCollapsed,
      isEmergencyCollapsed,
      isOtherDetailsCollapsed,
      toggleBasic,
      toggleInsurance,
      toggleAddress,
      toggleOtherAddress,
      toggleBilling,
      toggleCard,
      toggleAccount,
      toggleEmergency,
      toggleOtherDetails,
    } = useCollapsibleSections();

    const [countryPickerVisible, setCountryPickerVisible] = useState(false);

    // Expose methods through the ref
    React.useImperativeHandle(ref, () => ({
      getFieldVisibility: () => fieldVisibility,
    }));

    // Notify parent component when timezone dropdown state changes
    const handleTimezoneDropdownToggle = (isOpen) => {
      if (onTimezoneDropdownToggle) {
        onTimezoneDropdownToggle(isOpen);
      }
    };

    return (
      <View style={{ flex: 1 }}>
        <CollapsibleHeader
          title="Basic Details"
          isCollapsed={isBasicCollapsed}
          onToggle={toggleBasic}
        />

        {!isBasicCollapsed && (
          <View>
            {/* Basic Details Fields */}
            {basicDetailsFields.map((field) => (
              <FormField
                key={field.name}
                type="input"
                name={field.name}
                label={field.label}
                value={form[field.name]}
                onChangeText={(val) => handleChange(field.name, val)}
                error={errors[field.name]}
                isVisible={fieldVisibility[field.name]}
                onVisibilityToggle={() => handleVisibilityToggle(field.name)}
                disabled={field.disabled}
                fieldConfig={{
                  keyboardType: field.keyboardType,
                  required: field.required,
                }}
              />
            ))}

            {/* Additional Emails */}
            <AdditionalContactField
              type="email"
              items={additionalEmails}
              setItems={setAdditionalEmails}
              errors={errors}
            />

            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => setAdditionalEmails([...additionalEmails, ""])}
            >
              + Add more email
            </MyText>

            {/* Primary Phone */}
            <FormField
              type="phone"
              name="phone"
              label="Contact*"
              value={form.phone}
              onChangeText={(val) => handleChange("phone", val)}
              error={errors.phone}
              isVisible={true}
              onVisibilityToggle={() => {}}
              disabled={true}
              phoneInputRef={phoneInputRef}
            />

            {/* Additional Phones */}
            <AdditionalContactField
              type="phone"
              items={additionalPhones}
              setItems={setAdditionalPhones}
              errors={errors}
              phoneInputRef={phoneInputRef}
            />

            <MyText
              underline
              p
              medium
              style={{ color: colors.primary, marginBottom: 10 }}
              onPress={() => setAdditionalPhones([...additionalPhones, ""])}
            >
              + Add more contact
            </MyText>

            {/* Date of Birth */}
            <FormField
              type="date"
              name="dateOfBirth"
              label="Date of Birth*"
              value={form.dateOfBirth}
              onChangeDate={(date) =>
                handleChange("dateOfBirth", date.toISOString().split("T")[0])
              }
              error={errors.dateOfBirth}
              isVisible={fieldVisibility.dateOfBirth}
              onVisibilityToggle={() => handleVisibilityToggle("dateOfBirth")}
              fieldConfig={{
                placeholder: "Select date of birth",
                maximumDate: new Date(),
              }}
            />

            {/* Gender */}
            <FormField
              type="select"
              name="gender"
              label="Gender"
              value={form.gender}
              onSelect={(item) => handleChange("gender", item.value)}
              error={errors.gender}
              isVisible={fieldVisibility.gender}
              onVisibilityToggle={() => handleVisibilityToggle("gender")}
              data={genderOptions}
            />

            {/* Nationality */}
            <FormField
              type="nationality"
              name="nationality"
              label="Nationality"
              value={form.nationality}
              onSelect={(val) => handleChange("nationality", val)}
              error={errors.nationality}
              isVisible={fieldVisibility.nationality}
              onVisibilityToggle={() => handleVisibilityToggle("nationality")}
              countryPickerVisible={countryPickerVisible}
              setCountryPickerVisible={setCountryPickerVisible}
            />

            {/* Marital Status */}
            <FormField
              type="select"
              name="maritalStatus"
              label="Marital Status"
              value={form.maritalStatus}
              onSelect={(item) => handleChange("maritalStatus", item.value)}
              error={errors.maritalStatus}
              isVisible={fieldVisibility.maritalStatus}
              onVisibilityToggle={() => handleVisibilityToggle("maritalStatus")}
              data={maritalStatusOptions}
            />

            {/* Spouse Name - conditional */}
            {form.maritalStatus === "Married" && (
              <FormField
                type="input"
                name="spouseName"
                label="Spouse Name"
                value={form.spouseName}
                onChangeText={(val) => handleChange("spouseName", val)}
                error={errors.spouseName}
                isVisible={fieldVisibility.spouseName}
                onVisibilityToggle={() => handleVisibilityToggle("spouseName")}
              />
            )}
          </View>
        )}

        <CollapsibleHeader
          title="Home Address"
          isCollapsed={isAddressCollapsed}
          onToggle={toggleAddress}
        />
        {!isAddressCollapsed && (
          <AddressSection
            form={form}
            handleChange={handleChange}
            errors={errors}
            fieldVisibility={fieldVisibility}
            handleVisibilityToggle={handleVisibilityToggle}
          />
        )}

        <CollapsibleHeader
          title="Other Address"
          isCollapsed={isOtherAddressCollapsed}
          onToggle={toggleOtherAddress}
          showCheckbox
          checkboxText="Same as Home"
          isChecked={form.isSameAddress}
          onCheckboxToggle={() => {
            const newValue = !form.isSameAddress;
            handleChange("isSameAddress", newValue);

            if (newValue) {
              handleChange("otherApartment", form.apartment);
              handleChange("otherStreet", form.street);
              handleChange("otherCity", form.city);
              handleChange("otherState", form.state);
              handleChange("otherZipCode", form.zipCode);
              handleChange("otherCountry", form.country);
            }
          }}
        />
        {!isOtherAddressCollapsed && (
          <AddressSection
            form={form}
            handleChange={handleChange}
            errors={errors}
            fieldVisibility={fieldVisibility}
            handleVisibilityToggle={handleVisibilityToggle}
            prefix="other"
            disabled={form.isSameAddress}
            sourceForm={form}
          />
        )}
        <CollapsibleHeader
          title="Other Details"
          isCollapsed={isOtherDetailsCollapsed}
          onToggle={toggleOtherDetails}
        />
        {!isOtherDetailsCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            {/* Other Details Fields */}
            {otherDetailsFields.map((field) => (
              <FormField
                key={field.name}
                type="input"
                name={field.name}
                label={field.label}
                value={form[field.name]}
                onChangeText={(val) => handleChange(field.name, val)}
                error={errors[field.name]}
                isVisible={fieldVisibility[field.name]}
                onVisibilityToggle={() => handleVisibilityToggle(field.name)}
                fieldConfig={{
                  keyboardType: field.keyboardType,
                  placeholder: field.placeholder,
                }}
              />
            ))}

            {/* Secondary Phone */}
            <FormField
              type="phone"
              name="secondaryPhone"
              label="Secondary Phone No."
              value={form.secondaryPhone}
              onChangeText={(val) => handleChange("secondaryPhone", val)}
              error={errors.secondaryPhone}
              isVisible={fieldVisibility.secondaryPhone}
              onVisibilityToggle={() =>
                handleVisibilityToggle("secondaryPhone")
              }
              phoneInputRef={phoneInputRef}
            />

            {/* Timezone */}
            <FormField
              type="select"
              name="timezone"
              label="Timezone"
              value={form.timezone}
              onSelect={(item) => handleChange("timezone", item.value)}
              error={errors.timezone}
              isVisible={fieldVisibility.timezone}
              onVisibilityToggle={() => handleVisibilityToggle("timezone")}
              data={timezones}
              onDropdownToggle={handleTimezoneDropdownToggle}
            />
          </View>
        )}
        <CollapsibleHeader
          title="Emergency Contact Details"
          isCollapsed={isEmergencyCollapsed}
          onToggle={toggleEmergency}
        />
        {!isEmergencyCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            {/* Emergency Contact Fields */}
            {emergencyContactFields.map((field) => (
              <FormField
                key={field.name}
                type="input"
                name={field.name}
                label={field.label}
                value={form[field.name]}
                onChangeText={(val) => handleChange(field.name, val)}
                error={errors[field.name]}
                isVisible={fieldVisibility[field.name]}
                onVisibilityToggle={() => handleVisibilityToggle(field.name)}
                fieldConfig={{
                  keyboardType: field.keyboardType,
                }}
              />
            ))}

            {/* Emergency Phone */}
            <FormField
              type="phone"
              name="emergencyPhone"
              label="Emergency Phone No."
              value={form.emergencyPhone}
              onChangeText={(val) => handleChange("emergencyPhone", val)}
              error={errors.emergencyPhone}
              isVisible={fieldVisibility.emergencyPhone}
              onVisibilityToggle={() =>
                handleVisibilityToggle("emergencyPhone")
              }
              phoneInputRef={phoneInputRef}
            />
          </View>
        )}

        <CollapsibleHeader
          title="Health Insurance Information"
          isCollapsed={isInsuranceCollapsed}
          onToggle={toggleInsurance}
        />
        {!isInsuranceCollapsed && (
          <View style={{ zIndex: 1, position: "relative" }}>
            {/* Health Insurance Fields */}
            {healthInsuranceFields.map((field) => (
              <FormField
                key={field.name}
                type="input"
                name={field.name}
                label={field.label}
                value={form[field.name]}
                onChangeText={(val) => handleChange(field.name, val)}
                error={errors[field.name]}
                isVisible={fieldVisibility[field.name]}
                onVisibilityToggle={() => handleVisibilityToggle(field.name)}
                fieldConfig={{
                  keyboardType: field.keyboardType,
                }}
              />
            ))}

            {/* Date Fields */}
            <FormField
              type="date"
              name="effectiveDate"
              label="Effective Date"
              value={form.effectiveDate}
              onChangeDate={(date) =>
                handleChange("effectiveDate", date.toISOString().split("T")[0])
              }
              error={errors.effectiveDate}
              isVisible={fieldVisibility.effectiveDate}
              onVisibilityToggle={() => handleVisibilityToggle("effectiveDate")}
              fieldConfig={{
                placeholder: "Select effective date",
              }}
            />

            <FormField
              type="date"
              name="expirationDate"
              label="Expiration Date"
              value={form.expirationDate}
              onChangeDate={(date) =>
                handleChange("expirationDate", date.toISOString().split("T")[0])
              }
              error={errors.expirationDate}
              isVisible={fieldVisibility.expirationDate}
              onVisibilityToggle={() =>
                handleVisibilityToggle("expirationDate")
              }
              fieldConfig={{
                placeholder: "Select expiration date",
              }}
            />
          </View>
        )}

        <CollapsibleHeader
          title="Billing Address Details"
          isCollapsed={isBillingCollapsed}
          onToggle={toggleBilling}
          showCheckbox
          checkboxText="Same as Home"
          isChecked={form.isSameBillingAddress}
          onCheckboxToggle={() => {
            const newValue = !form.isSameBillingAddress;
            handleChange("isSameBillingAddress", newValue);

            if (newValue) {
              handleChange("billingApartment", form.apartment);
              handleChange("billingStreet", form.street);
              handleChange("billingCity", form.city);
              handleChange("billingState", form.state);
              handleChange("billingZipCode", form.zipCode);
              handleChange("billingCountry", form.country);
            }
          }}
        />
        {!isBillingCollapsed && (
          <AddressSection
            form={form}
            handleChange={handleChange}
            errors={errors}
            fieldVisibility={fieldVisibility}
            handleVisibilityToggle={handleVisibilityToggle}
            prefix="billing"
            disabled={form.isSameBillingAddress}
            sourceForm={form}
          />
        )}

        <CollapsibleHeader
          title="Card Details"
          isCollapsed={isCardCollapsed}
          onToggle={toggleCard}
        />
        {!isCardCollapsed && (
          <CardDetailsSection
            form={form}
            handleChange={handleChange}
            errors={errors}
            setErrors={setErrors}
            fieldVisibility={fieldVisibility}
            handleVisibilityToggle={handleVisibilityToggle}
          />
        )}

        <CollapsibleHeader
          title="Bank Account Details"
          isCollapsed={isAccountCollapsed}
          onToggle={toggleAccount}
        />
        {!isAccountCollapsed && (
          <BankAccountSection
            form={form}
            handleChange={handleChange}
            errors={errors}
            fieldVisibility={fieldVisibility}
            handleVisibilityToggle={handleVisibilityToggle}
          />
        )}
      </View>
    );
  }
);

PersonalSection.propTypes = {
  form: PropTypes.shape({
    firstName: PropTypes.string,
    middleName: PropTypes.string,
    lastName: PropTypes.string,
    nickname: PropTypes.string,
    email: PropTypes.string,
    phone: PropTypes.string,
    dateOfBirth: PropTypes.string,
    gender: PropTypes.string,
    nationality: PropTypes.string,
    maritalStatus: PropTypes.string,
    spouseName: PropTypes.string,
    // Health Insurance Information
    policyNumber: PropTypes.string,
    insuranceProvider: PropTypes.string,
    policyPeriod: PropTypes.string,
    effectiveDate: PropTypes.string,
    expirationDate: PropTypes.string,
    sumInsured: PropTypes.string,
    // Address fields
    apartment: PropTypes.string,
    street: PropTypes.string,
    city: PropTypes.string,
    state: PropTypes.string,
    zipCode: PropTypes.string,
    country: PropTypes.string,
    // Billing Address fields
    isSameBillingAddress: PropTypes.bool,
    billingApartment: PropTypes.string,
    billingStreet: PropTypes.string,
    billingCity: PropTypes.string,
    billingState: PropTypes.string,
    billingZipCode: PropTypes.string,
    billingCountry: PropTypes.string,
    // Card Details fields
    cardName: PropTypes.string,
    cardNumber: PropTypes.string,
    cardExpiry: PropTypes.string,
    cardCvv: PropTypes.string,
    // Bank Account Details fields
    accountName: PropTypes.string,
    bankName: PropTypes.string,
    accountNumber: PropTypes.string,
    ifscCode: PropTypes.string,
    // Other Address fields
    isSameAddress: PropTypes.bool,
    otherApartment: PropTypes.string,
    otherStreet: PropTypes.string,
    otherCity: PropTypes.string,
    otherState: PropTypes.string,
    otherZipCode: PropTypes.string,
    otherCountry: PropTypes.string,
    // Other Details fields
    secondaryEmail: PropTypes.string,
    secondaryPhone: PropTypes.string,
    personalWebsite: PropTypes.string,
    hobbies: PropTypes.string,
    religion: PropTypes.string,
    preferredContactMethod: PropTypes.string,
    timezone: PropTypes.string,
    // Emergency Contact fields
    emergencyContactName: PropTypes.string,
    emergencyContactRelationship: PropTypes.string,
    emergencyEmail: PropTypes.string,
    emergencyPhone: PropTypes.string,
    emergencyAddress: PropTypes.string,
  }).isRequired,
  handleChange: PropTypes.func.isRequired,
  errors: PropTypes.object,
  setErrors: PropTypes.func,
  phoneInputRef: PropTypes.object,
  onTimezoneDropdownToggle: PropTypes.func,
};

export default PersonalSection;
