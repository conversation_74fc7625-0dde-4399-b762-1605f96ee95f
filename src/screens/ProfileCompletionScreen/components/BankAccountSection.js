import React from 'react';
import { View } from 'react-native';
import <PERSON><PERSON><PERSON> from './FormField';
import { formatIFSCCode, formatNumericInput } from '../utils/validationUtils';
import { bankAccountFields } from '../utils/fieldConfigurations';

/**
 * Specialized component for bank account details with validation and formatting
 * @param {Object} props - Component props
 * @param {Object} props.form - Form data
 * @param {Function} props.handleChange - Change handler
 * @param {Object} props.errors - Error messages
 * @param {Object} props.fieldVisibility - Field visibility state
 * @param {Function} props.handleVisibilityToggle - Visibility toggle handler
 * @returns {JSX.Element} Bank account section component
 */
const BankAccountSection = ({
  form,
  handleChange,
  errors,
  fieldVisibility,
  handleVisibilityToggle,
}) => {
  const handleAccountNumberChange = (val) => {
    const numericValue = formatNumericInput(val);
    handleChange("accountNumber", numericValue);
  };

  const handleIFSCCodeChange = (val) => {
    const formattedValue = formatIFSCCode(val);
    handleChange("ifscCode", formattedValue);
  };

  const renderField = (field) => {
    let onChangeText = (val) => handleChange(field.name, val);
    
    // Apply special formatting for specific fields
    if (field.name === 'accountNumber') {
      onChangeText = handleAccountNumberChange;
    } else if (field.name === 'ifscCode') {
      onChangeText = handleIFSCCodeChange;
    }

    return (
      <FormField
        key={field.name}
        type="input"
        name={field.name}
        label={field.label}
        value={form[field.name]}
        onChangeText={onChangeText}
        error={errors[field.name]}
        isVisible={fieldVisibility[field.name]}
        onVisibilityToggle={() => handleVisibilityToggle(field.name)}
        fieldConfig={{
          placeholder: field.placeholder,
          keyboardType: field.keyboardType,
          autoCapitalize: field.autoCapitalize,
          maxLength: field.maxLength,
        }}
      />
    );
  };

  return (
    <View style={{ zIndex: 1, position: "relative" }}>
      {bankAccountFields.map(renderField)}
    </View>
  );
};

export default BankAccountSection;
