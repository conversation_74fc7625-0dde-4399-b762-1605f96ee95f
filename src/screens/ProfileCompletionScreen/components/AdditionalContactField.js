import React from 'react';
import { View, TouchableOpacity, Image } from 'react-native';
import MyText from '../../../components/MyText';
import InputField from '../../../components/InputField';
import PhoneNumberField from '../../../components/PhoneNumberField';
import icons from '../../../assets/icons';
import commonStyles from '../../../assets/commonStyles';

/**
 * Reusable component for additional email/phone fields with remove functionality
 * @param {Object} props - Component props
 * @param {string} props.type - Field type ('email' or 'phone')
 * @param {Array} props.items - Array of additional items
 * @param {Function} props.setItems - Function to update items array
 * @param {Object} props.errors - Error messages
 * @param {Object} props.phoneInputRef - Phone input reference (for phone type)
 * @returns {JSX.Element} Additional contact field component
 */
const AdditionalContactField = ({
  type,
  items,
  setItems,
  errors,
  phoneInputRef,
}) => {
  const isEmail = type === 'email';
  const fieldLabel = isEmail ? 'Additional Email' : 'Add Contact';
  const placeholder = isEmail ? 'Add additional email' : '';

  const handleRemoveItem = (index) => {
    const updated = items.filter((_, i) => i !== index);
    setItems(updated);
  };

  const handleUpdateItem = (index, value) => {
    const updated = [...items];
    updated[index] = value;
    setItems(updated);
  };

  return (
    <>
      {items.map((item, idx) => (
        <View key={`additional-${type}-${idx}`}>
          {/* Custom label with cross button */}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 6,
            }}
          >
            <MyText p medium style={{ color: "#000" }}>
              {`${fieldLabel} ${idx + 1}`}
            </MyText>
            <TouchableOpacity
              onPress={() => handleRemoveItem(idx)}
              style={{ padding: 4 }}
            >
              <Image
                source={icons.closeIcon}
                resizeMode="contain"
                style={commonStyles.smallIcon}
              />
            </TouchableOpacity>
          </View>

          {/* Input field */}
          {isEmail ? (
            <InputField
              placeholder={`${placeholder} ${idx + 1}`}
              value={item}
              onChangeText={(val) => handleUpdateItem(idx, val)}
              keyboardType="email-address"
              showVisibilityToggle={true}
              isVisible={true}
              onVisibilityToggle={() => {}}
              error={errors[`additional${type.charAt(0).toUpperCase()}${type.slice(1)}${idx}`]}
            />
          ) : (
            <PhoneNumberField
              value={item}
              phoneInputRef={phoneInputRef}
              onChangeRaw={(val) => handleUpdateItem(idx, val)}
              setCountryCode={() => {}}
              error={errors[`additional${type.charAt(0).toUpperCase()}${type.slice(1)}${idx}`]}
              setError={() => {}}
              showVisibilityToggle={true}
              isVisible={true}
              onVisibilityToggle={() => {}}
            />
          )}
        </View>
      ))}
    </>
  );
};

export default AdditionalContactField;
