import React from 'react';
import { View } from 'react-native';
import <PERSON><PERSON><PERSON> from './FormField';
import { addressFields } from '../utils/fieldConfigurations';

/**
 * Reusable address section component
 * @param {Object} props - Component props
 * @param {Object} props.form - Form data
 * @param {Function} props.handleChange - Change handler
 * @param {Object} props.errors - Error messages
 * @param {Object} props.fieldVisibility - Field visibility state
 * @param {Function} props.handleVisibilityToggle - Visibility toggle handler
 * @param {string} props.prefix - Field name prefix (e.g., 'other', 'billing')
 * @param {boolean} props.disabled - Disabled state for all fields
 * @param {Object} props.sourceForm - Source form data for copying values (when "same as home" is checked)
 * @returns {JSX.Element} Address section component
 */
const AddressSection = ({
  form,
  handleChange,
  errors,
  fieldVisibility,
  handleVisibilityToggle,
  prefix = '',
  disabled = false,
  sourceForm = null,
}) => {
  const getFieldName = (baseName) => prefix ? `${prefix}${baseName.charAt(0).toUpperCase()}${baseName.slice(1)}` : baseName;
  const getFieldValue = (baseName) => {
    const fieldName = getFieldName(baseName);
    return disabled && sourceForm ? sourceForm[baseName] : form[fieldName];
  };

  return (
    <View style={{ zIndex: 1, position: "relative" }}>
      {addressFields.map((field) => {
        const fieldName = getFieldName(field.name);
        return (
          <FormField
            key={fieldName}
            type="input"
            name={fieldName}
            label={prefix ? `${prefix.charAt(0).toUpperCase()}${prefix.slice(1)} ${field.label}` : field.label}
            value={getFieldValue(field.name)}
            onChangeText={(val) => handleChange(fieldName, val)}
            error={errors[fieldName]}
            isVisible={fieldVisibility[fieldName]}
            onVisibilityToggle={() => handleVisibilityToggle(fieldName)}
            disabled={disabled}
          />
        );
      })}
    </View>
  );
};

export default AddressSection;
