import React from 'react';
import { View } from 'react-native';
import <PERSON><PERSON><PERSON> from './FormField';
import { 
  validateCardExpiry, 
  validateCardNumber, 
  formatCardNumber, 
  formatCardExpiry, 
  formatNumericInput 
} from '../utils/validationUtils';

/**
 * Specialized component for card details with validation and formatting
 * @param {Object} props - Component props
 * @param {Object} props.form - Form data
 * @param {Function} props.handleChange - Change handler
 * @param {Object} props.errors - Error messages
 * @param {Function} props.setErrors - Error setter
 * @param {Object} props.fieldVisibility - Field visibility state
 * @param {Function} props.handleVisibilityToggle - Visibility toggle handler
 * @returns {JSX.Element} Card details section component
 */
const CardDetailsSection = ({
  form,
  handleChange,
  errors,
  setErrors,
  fieldVisibility,
  handleVisibilityToggle,
}) => {
  const handleCardNumberChange = (val) => {
    const formattedValue = formatCardNumber(val);
    handleChange("cardNumber", formattedValue);

    const validationError = validateCardNumber(formattedValue);
    if (validationError) {
      setErrors((prev) => ({
        ...prev,
        cardNumber: validationError,
      }));
    } else if (errors.cardNumber) {
      setErrors((prev) => ({
        ...prev,
        cardNumber: "",
      }));
    }
  };

  const handleCardExpiryChange = (val) => {
    const formattedValue = formatCardExpiry(val);
    handleChange("cardExpiry", formattedValue);

    if (formattedValue.length === 5) {
      const validationError = validateCardExpiry(formattedValue);
      if (validationError) {
        setErrors((prev) => ({
          ...prev,
          cardExpiry: validationError,
        }));
      } else if (errors.cardExpiry) {
        setErrors((prev) => ({
          ...prev,
          cardExpiry: "",
        }));
      }
    }
  };

  const handleCvvChange = (val) => {
    const numericValue = formatNumericInput(val);
    handleChange("cardCvv", numericValue);
  };

  return (
    <View style={{ zIndex: 1, position: "relative" }}>
      {/* Name on Card */}
      <FormField
        type="input"
        name="cardName"
        label="Name on Card"
        value={form.cardName}
        onChangeText={(val) => handleChange("cardName", val)}
        error={errors.cardName}
        isVisible={fieldVisibility.cardName}
        onVisibilityToggle={() => handleVisibilityToggle("cardName")}
        fieldConfig={{
          placeholder: "Enter name as it appears on card",
        }}
      />

      {/* Card Number */}
      <FormField
        type="input"
        name="cardNumber"
        label="Card Number"
        value={form.cardNumber}
        onChangeText={handleCardNumberChange}
        error={errors.cardNumber}
        isVisible={fieldVisibility.cardNumber}
        onVisibilityToggle={() => handleVisibilityToggle("cardNumber")}
        fieldConfig={{
          keyboardType: "numeric",
          maxLength: 19,
        }}
      />

      {/* Expiry and CVV Row */}
      <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
        <View style={{ width: "48%" }}>
          <FormField
            type="input"
            name="cardExpiry"
            label="Expiry Date (MM/YY)"
            value={form.cardExpiry}
            onChangeText={handleCardExpiryChange}
            error={errors.cardExpiry}
            isVisible={fieldVisibility.cardExpiry}
            onVisibilityToggle={() => handleVisibilityToggle("cardExpiry")}
            fieldConfig={{
              placeholder: "MM/YY",
              keyboardType: "numeric",
              maxLength: 5,
            }}
          />
        </View>
        <View style={{ width: "48%" }}>
          <FormField
            type="input"
            name="cardCvv"
            label="CVV"
            value={form.cardCvv}
            onChangeText={handleCvvChange}
            error={errors.cardCvv}
            isVisible={fieldVisibility.cardCvv}
            onVisibilityToggle={() => handleVisibilityToggle("cardCvv")}
            fieldConfig={{
              keyboardType: "numeric",
              maxLength: 4,
            }}
          />
        </View>
      </View>
    </View>
  );
};

export default CardDetailsSection;
