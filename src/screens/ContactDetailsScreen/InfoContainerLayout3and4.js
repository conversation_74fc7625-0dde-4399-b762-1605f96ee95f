import {
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Dimensions,
  Linking,
} from "react-native";
import colors from "../../assets/colors";
import icons from "../../assets/icons";
import MyText from "../../components/MyText";
import { useState } from "react";
import { formatDate } from "../../utils/commonHelpers";
import commonStyles from "../../assets/commonStyles";

const InfoContainerLayout3and4 = ({
  contact,
  toggleFavorite,
  isFavorite,
  renderDetailItem,
}) => {


const renderDetailItemTwo = (label, value, noLine, icon) => {
  if (!value) return null;

  const isUrl = typeof value === "string" && value.startsWith("http");
  const isPhone = typeof value === "string" && /^[\d\s()+-]+$/.test(value);
  const isEmail = typeof value === "string" && /\S+@\S+\.\S+/.test(value);

  const handlePress = () => {
    if (isUrl) {
      Linking.openURL(value);
    } else if (isPhone) {
      Linking.openURL(`tel:${value}`);
    } else if (isEmail) {
      Linking.openURL(`mailto:${value}`);
    }
  };

  const Content = (
    <>
      <View style={styles.detailItem} key={label}>
        <View
          style={[
            commonStyles.rowWithoutSpaceBetween,
            { alignItems: "center" },
          ]}
        >
          <Image
            source={icon}
            resizeMode="contain"
            style={[styles.arrow, { tintColor: colors.primary }]}
          />
          <MyText bold style={styles.label}>
            {label}
          </MyText>
        </View>

        <MyText
          style={[
            styles.value,
            (isUrl || isPhone || isEmail) && {
              color: "blue",
              textDecorationLine: "underline",
            },
          ]}
        >
          {value}
        </MyText>
      </View>
      {!noLine && (
        <View
          style={{
            borderBottomWidth: 1,
            borderBottomColor: "#ccc",
            marginVertical: 10,
          }}
        />
      )}
    </>
  );

  return (isUrl || isPhone || isEmail) ? (
    <TouchableOpacity onPress={handlePress} activeOpacity={0.7}>
      {Content}
    </TouchableOpacity>
  ) : (
    Content
  );
};

  const [expandedSections, setExpandedSections] = useState([]);
  const toggleSection = (section) => {
    setExpandedSections((prev) =>
      prev.includes(section)
        ? prev.filter((item) => item !== section)
        : [...prev, section]
    );
  };

  const officialAddressParts = [
    contact?.addresses_home?.apartment,
    contact?.addresses_home?.street,
    contact?.addresses_home?.city,
    contact?.addresses_home?.state,
    contact?.addresses_home?.country,
    contact?.addresses_home?.postalCode,
  ];
  const formattedAddress = officialAddressParts.filter(Boolean).join(", ");

  const otherAddressParts = [
    contact?.addresses_other?.apartment,
    contact?.addresses_other?.street,
    contact?.addresses_other?.city,
    contact?.addresses_other?.state,
    contact?.addresses_other?.country,
    contact?.addresses_other?.postalCode,
  ];
  const formattedOtherAddress = otherAddressParts.filter(Boolean).join(", ");

  return (
    <View style={styles.infoContainer}>
      {Array.isArray(contact?.tags) && contact.tags.length > 0 &&
  renderDetailItemTwo("Tags", contact.tags[0], false, icons.tagIcon)}

      {contact?.phoneNumbers?.map((phone, index) => (
        <View key={index}>
          {renderDetailItemTwo(
            `Mobile ${index + 1}`,
            phone?.number
              ? `${phone.countryCode?.startsWith('+') ? phone.countryCode : `+${phone.countryCode || ''}`} ${phone.number}`
              : "No phone number available",
            false,
            icons.callIcon
          )}
        </View>
      ))}

      {/* {renderDetailItemTwo("Email", contact?.emails?.[0]?.address)} */}
      {contact?.emails?.map((email, index) => (
        <View key={index}>
          {renderDetailItemTwo(
            `Email ${index + 1}`,
            email?.address ? email?.address : "No email available",
            false,
            icons.emailIconUnfilled
          )}
        </View>
      ))}

      {renderDetailItemTwo(
        "Company",
        contact?.business_details?.companyInformation?.company_name,
        false,
        icons.companyIcon
      )}

      {["Personal Details", "Business Details", "Social Details"].map(
        (section) => (
          <View key={section}>
            <TouchableOpacity
              onPress={() => toggleSection(section)}
              style={styles.sectionHeader}
            >
              <View
                style={[
                  commonStyles.rowWithoutSpaceBetween,
                  { alignItems: "center" },
                ]}
              >
                <Image
                  source={
                    section === "Personal Details"
                      ? icons.profilesIcon
                      : section === "Business Details"
                      ? icons.businessDetailsIcon
                      : icons.socialDetailsIcon
                  }
                  resizeMode="contain"
                  style={[styles.arrow, { tintColor: colors.primary }]}
                />
                <MyText style={styles.sectionTitle}>{section}</MyText>
              </View>

              <Image
                source={icons.filledArrowDown}
                style={[
                  styles.arrow,
                  {
                    transform: [
                      {
                        rotate: expandedSections.includes(section)
                          ? "180deg"
                          : "0deg",
                      },
                    ],
                  },
                ]}
                resizeMode="contain"
              />
            </TouchableOpacity>

            {expandedSections.includes(section) && (
              <View style={styles.sectionContent}>
                {section === "Personal Details" && (
                  <>
                    <MyText h5 bold style={{ marginBottom: 10 }}>
                      • Basic Details
                    </MyText>
                    {renderDetailItem(
                      "Date of Birth",
                      contact?.dateOfBirth
                        ? formatDate(contact?.dateOfBirth)
                        : ""
                    )}
                    {renderDetailItem("Gender", contact?.gender)}

                    {renderDetailItem(
                      "Official Address",
                      formattedAddress || "Not available"
                    )}

                    {renderDetailItem(
                      "Other Address",
                      formattedOtherAddress || "Not available"
                    )}
                    {/* Other address array */}
                    {contact?.addresses?.map((address, index) => (
                      <View key={index}>
                        {renderDetailItem(
                          `Other Address ${index + 2}`,
                          `${address?.apartment}, ${address?.street}, ${address?.city}, ${address?.state}, ${address?.country}, ${address?.postalCode}`
                        )}
                      </View>
                    ))}

                    {renderDetailItem("Nationality", contact?.nationality)}
                    <MyText h5 bold style={{ marginBottom: 10 }}>
                      • Other Details
                    </MyText>
                    {renderDetailItem("Nickname", contact?.nickname)}
                    {renderDetailItem(
                      "Languages",
                      contact?.languages?.join(", ")
                    )}

                    {/* timeZone */}
                    {renderDetailItem(
                      "Time Zone",
                      contact?.timeZone
                        ? contact?.timeZone
                        : "No time zone available"
                    )}

                    {/* Religion */}
                    {renderDetailItem(
                      "Religion",
                      contact?.religion
                        ? contact?.religion
                        : "No religion available"
                    )}

                    {renderDetailItem("Marital Status", contact?.maritalStatus)}
                    {renderDetailItem("Hobbies", contact?.hobbies?.join(", "))}
                    {renderDetailItem(
                      "Emergency Contact",
                      contact?.emergency_contact?.contactName &&
                        contact?.emergency_contact?.phoneNumber
                        ? `${contact?.emergency_contact?.contactName} (${contact?.emergency_contact?.phoneNumber_country_code}${contact?.emergency_contact?.phoneNumber}) (${contact?.emergency_contact?.relationship})`
                        : contact?.emergency_contact?.contactName ||
                            contact?.emergency_contact?.phoneNumber
                    )}
                    {renderDetailItem(
                      "Health Insurance",
                      contact?.healthInsurance?.policyNumber &&
                        contact?.healthInsurance?.effectiveDate &&
                        contact?.healthInsurance?.expirationDate
                        ? `${
                            contact?.healthInsurance?.policyNumber
                          } (${formatDate(
                            contact?.healthInsurance?.effectiveDate
                          )} - ${formatDate(
                            contact?.healthInsurance?.expirationDate
                          )})`
                        : contact?.healthInsurance?.policyNumber ||
                            contact?.healthInsurance?.effectiveDate ||
                            contact?.healthInsurance?.expirationDate
                    )}
                    {renderDetailItem(
                      "Billing Address",
                      contact?.billing_address?.officeBuilding &&
                        contact?.billing_address?.street &&
                        contact?.billing_address?.city &&
                        contact?.billing_address?.state &&
                        contact?.billing_address?.postalCode &&
                        contact?.billing_address?.country
                        ? `${contact?.billing_address?.officeBuilding} (${contact?.billing_address?.street}, ${contact?.billing_address?.city}, ${contact?.billing_address?.state}, ${contact?.billing_address?.postalCode}, ${contact?.billing_address?.country})`
                        : contact?.billing_address?.officeBuilding ||
                            contact?.billing_address?.street ||
                            contact?.billing_address?.city ||
                            contact?.billing_address?.state ||
                            contact?.billing_address?.postalCode ||
                            contact?.billing_address?.country
                    )}
                    {renderDetailItem(
                      "Account Details",
                      contact?.account_details?.name &&
                        contact?.account_details?.bank &&
                        contact?.account_details?.accountNumber &&
                        contact?.account_details?.ifscCode &&
                        contact?.account_details?.paypalEmail
                        ? `${contact?.account_details?.name} (${contact?.account_details?.bank}, ${contact?.account_details?.accountNumber}, ${contact?.account_details?.ifscCode}, ${contact?.account_details?.paypalEmail})`
                        : contact?.account_details?.name ||
                            contact?.account_details?.bank ||
                            contact?.account_details?.accountNumber ||
                            contact?.account_details?.ifscCode ||
                            contact?.account_details?.paypalEmail
                    )}
                    {renderDetailItem(
                      "Card Details",
                      contact?.card_details?.nameOnCard &&
                        contact?.card_details?.cardNumber &&
                        contact?.card_details?.expiryDate
                        ? `${contact?.card_details?.nameOnCard} (${contact?.card_details?.cardNumber}) (${contact?.card_details?.expiryDate})`
                        : contact?.card_details?.nameOnCard ||
                            contact?.card_details?.cardNumber ||
                            contact?.card_details?.expiryDate,
                      true
                    )}

                    {/* CryptoWallet */}
                    {renderDetailItem(
                      "Crypto Wallet",
                      contact?.crypto_wallet?.wallet_address &&
                        contact?.crypto_wallet?.wallet_type
                        ? `${contact?.crypto_wallet?.wallet_address} (${contact?.crypto_wallet?.wallet_type})`
                        : contact?.crypto_wallet?.wallet_address ||
                            contact?.crypto_wallet?.wallet_type,
                      true
                    )}
                  </>
                )}

                {section === "Business Details" && (
                  <>
                    <MyText h5 bold style={{ marginBottom: 10 }}>
                      • Employee Info
                    </MyText>
                    {renderDetailItem(
                      "Job Title",
                      contact?.business_details?.jobTitle
                    )}

                    {renderDetailItem(
                      "Company Name",
                      contact?.business_details?.companyInformation
                        ?.company_name
                    )}
                    {renderDetailItem(
                      "Department",
                      contact?.business_details?.department
                    )}
                    {renderDetailItem(
                      "Work Phone Number",
                      contact?.business_details?.workContact?.phoneNumber
                    )}
                    {renderDetailItem(
                      "Work Fax Number",
                      contact?.business_details?.workContact?.fax
                    )}
                    {renderDetailItem(
                      "Work Email",
                      contact?.business_details?.workContact?.email
                    )}

                    {/* "resume": "john_doe_resume.pdf", */}

                    {renderDetailItem(
                      "Resume",
                      contact?.business_details?.resume
                        ? contact?.business_details?.resume
                        : "No resume available"
                    )}

                    {renderDetailItem(
                      "LinkedIn Profile",
                      contact?.business_details?.companySocialMedia?.linkedin
                        ?.url
                        ? contact?.business_details?.companySocialMedia
                            ?.linkedin?.url
                        : "No LinkedIn profile available"
                    )}
                    {/* twitter */}
                    {renderDetailItem(
                      "Twitter Profile",
                      contact?.business_details?.companySocialMedia?.twitter
                        ?.url
                        ? contact?.business_details?.companySocialMedia?.twitter
                            ?.url
                        : "No Twitter profile available"
                    )}
                    {/* work schedule */}
                    {renderDetailItem(
                      "Work Schedule",
                      contact?.business_details?.workSchedule
                        ? contact?.business_details?.workSchedule
                        : "No work schedule available"
                    )}

                    {contact?.business_details?.certifications?.map(
                      (certification, index) =>
                        renderDetailItem(
                          `Professional Certification ${index + 1}`,
                          certification?.url
                        )
                    )}

                    {renderDetailItem(
                      "Industry",
                      contact?.business_details?.industry
                    )}

                    <MyText h5 bold style={{ marginBottom: 10 }}>
                      • Company Info
                    </MyText>

                    {renderDetailItem(
                      " Name",
                      contact?.business_details?.companyInformation
                        ?.company_name
                    )}
                    {/* Company logo */}
                    {renderDetailItem(
                      "Company Logo",
                      contact?.business_details?.companyInformation
                        ?.company_logo
                        ? contact?.business_details?.companyInformation
                            ?.company_logo
                        : "No company logo available"
                    )}

                    {/* Company phone number */}
                    {renderDetailItem(
                      "Company Phone Number",
                      contact?.business_details?.companyInformation?.phone
                        ? contact?.business_details?.companyInformation?.phone
                        : "No company phone number available"
                    )}

                    {/* company email */}
                    {renderDetailItem(
                      "Company Email",
                      contact?.business_details?.companyInformation?.email
                        ? contact?.business_details?.companyInformation?.email
                        : "No company email available"
                    )}

                    {/* Company Fax address */}
                    {renderDetailItem(
                      "Company Fax",
                      contact?.business_details?.companyInformation?.fax
                        ? contact?.business_details?.companyInformation?.fax
                        : "No company fax available"
                    )}

                    {/* Company website */}
                    {renderDetailItem(
                      "Company Website",
                      contact?.business_details?.companyInformation?.website
                        ? contact?.business_details?.companyInformation?.website
                        : "No company website available"
                    )}

                    {renderDetailItem(
                      "Company Address",
                      [
                        contact?.business_details?.business_address
                          ?.officeBuilding,
                        contact?.business_details?.business_address?.street,
                        contact?.business_details?.business_address?.city,
                        contact?.business_details?.business_address?.state,
                        contact?.business_details?.business_address?.country,
                        contact?.business_details?.business_address?.postalCode,
                      ]
                        .filter(Boolean)
                        .join(", ") || "Not available"
                    )}

                    {/* Billing address */}
                    {renderDetailItem(
                      "Billing Address",
                      [
                        contact?.business_details?.billing_address
                          ?.officeBuilding,
                        contact?.business_details?.billing_address?.street,
                        contact?.business_details?.billing_address?.city,
                        contact?.business_details?.billing_address?.state,
                        contact?.business_details?.billing_address?.country,
                        contact?.business_details?.billing_address?.postalCode,
                      ]
                        .filter(Boolean)
                        .join(", ") || "Not available"
                    )}

                    {/* Company card details */}
                    {renderDetailItem(
                      "Company Card Details",
                      contact?.business_details?.card_details?.cardNumber &&
                        contact?.business_details?.card_details?.expiryDate
                        ? `${contact?.business_details?.card_details?.nameOnCard} ${contact?.business_details?.card_details?.cardNumber} (${contact?.business_details?.card_details?.expiryDate})`
                        : contact?.business_details?.card_details?.cardNumber ||
                            contact?.business_details?.card_details?.expiryDate
                    )}

                    {/* Company account_details */}
                    {renderDetailItem(
                      "Company Account Details",
                      contact?.business_details?.account_details?.name &&
                        contact?.business_details?.account_details?.bank &&
                        contact?.business_details?.account_details
                          ?.accountNumber &&
                        contact?.business_details?.account_details?.ifscCode &&
                        contact?.business_details?.account_details?.paypalEmail
                        ? `${contact?.business_details?.account_details?.name} (${contact?.business_details?.account_details?.bank}, ${contact?.business_details?.account_details?.accountNumber}, ${contact?.business_details?.account_details?.ifscCode}, ${contact?.business_details?.account_details?.paypalEmail})`
                        : contact?.business_details?.account_details?.name ||
                            contact?.business_details?.account_details?.bank ||
                            contact?.business_details?.account_details
                              ?.accountNumber ||
                            contact?.business_details?.account_details
                              ?.ifscCode ||
                            contact?.business_details?.account_details
                              ?.paypalEmail
                    )}

                    {/* Company social media */}
                    {renderDetailItem(
                      "Company Facebook",
                      contact?.business_details?.companySocialMedia?.facebook
                        ?.url
                        ? contact?.business_details?.companySocialMedia
                            ?.facebook?.url
                        : "No company Facebook available"
                    )}
                    {renderDetailItem(
                      "Company Instagram",
                      contact?.business_details?.companySocialMedia?.instagram
                        ?.url
                        ? contact?.business_details?.companySocialMedia
                            ?.instagram?.url
                        : "No company Instagram available"
                    )}
                    {renderDetailItem(
                      "Company Twitter",
                      contact?.business_details?.companySocialMedia?.twitter
                        ?.url
                        ? contact?.business_details?.companySocialMedia?.twitter
                            ?.url
                        : "No company Twitter available"
                    )}
                    {renderDetailItem(
                      "Company LinkedIn",
                      contact?.business_details?.companySocialMedia?.linkedin
                        ?.url
                        ? contact?.business_details?.companySocialMedia
                            ?.linkedin?.url
                        : "No company LinkedIn available"
                    )}
                    {renderDetailItem(
                      "Company Snapchat",
                      contact?.business_details?.companySocialMedia?.snapchat
                        ?.url
                        ? contact?.business_details?.companySocialMedia
                            ?.snapchat?.url
                        : "No company Snapchat available"
                    )}
                    {renderDetailItem(
                      "Company WhatsApp",
                      contact?.business_details?.companySocialMedia?.whatsapp
                        ?.url
                        ? contact?.business_details?.companySocialMedia
                            ?.whatsapp?.url
                        : "No company WhatsApp available"
                    )}
                    {renderDetailItem(
                      "Company Telegram",
                      contact?.business_details?.companySocialMedia?.telegram
                        ?.url
                        ? contact?.business_details?.companySocialMedia
                            ?.telegram?.url
                        : "No company Telegram available"
                    )}
                    {renderDetailItem(
                      "Company Signal",
                      contact?.business_details?.companySocialMedia?.signal?.url
                        ? contact?.business_details?.companySocialMedia?.signal
                            ?.url
                        : "No company Signal available"
                    )}
                    {renderDetailItem(
                      "Company Skype",
                      contact?.business_details?.companySocialMedia?.skype?.url
                        ? contact?.business_details?.companySocialMedia?.skype
                            ?.url
                        : "No company Skype available"
                    )}
                    {renderDetailItem(
                      "Company YouTube",
                      contact?.business_details?.companySocialMedia?.youtube
                        ?.url
                        ? contact?.business_details?.companySocialMedia?.youtube
                            ?.url
                        : "No company YouTube available"
                    )}
                    {renderDetailItem(
                      "Company Twitch",
                      contact?.business_details?.companySocialMedia?.twitch?.url
                        ? contact?.business_details?.companySocialMedia?.twitch
                            ?.url
                        : "No company Twitch available"
                    )}
                    {renderDetailItem(
                      "Company TikTok",
                      contact?.business_details?.companySocialMedia?.tiktok?.url
                        ? contact?.business_details?.companySocialMedia?.tiktok
                            ?.url
                        : "No company TikTok available",
                      true
                    )}

                    {/* company messenger IDs */}
                    {renderDetailItem(
                      "Company iMessage",
                      contact?.business_details?.companyMessengerIds?.iMessage
                        ?.url
                        ? contact?.business_details?.companyMessengerIds
                            ?.iMessage?.url
                        : "No company iMessage available"
                    )}
                    {renderDetailItem(
                      "Company Google Chat",
                      contact?.business_details?.companyMessengerIds?.googleChat
                        ?.url
                        ? contact?.business_details?.companyMessengerIds
                            ?.googleChat?.url
                        : "No company Google Chat available"
                    )}
                    {renderDetailItem(
                      "Company Discord",
                      contact?.business_details?.companyMessengerIds?.discord
                        ?.url
                        ? contact?.business_details?.companyMessengerIds
                            ?.discord?.url
                        : "No company Discord available"
                    )}
                    {renderDetailItem(
                      "Company Slack",
                      contact?.business_details?.companyMessengerIds?.slack?.url
                        ? contact?.business_details?.companyMessengerIds?.slack
                            ?.url
                        : "No company Slack available"
                    )}
                    {renderDetailItem(
                      "Company WeChat",
                      contact?.business_details?.companyMessengerIds?.wechat
                        ?.url
                        ? contact?.business_details?.companyMessengerIds?.wechat
                            ?.url
                        : "No company WeChat available"
                    )}
                    {renderDetailItem(
                      "Company Kik",
                      contact?.business_details?.companyMessengerIds?.kik?.url
                        ? contact?.business_details?.companyMessengerIds?.kik
                            ?.url
                        : "No company Kik available"
                    )}
                    {renderDetailItem(
                      "Company Line",
                      contact?.business_details?.companyMessengerIds?.line?.url
                        ? contact?.business_details?.companyMessengerIds?.line
                            ?.url
                        : "No company Line available",
                      true // No line for the last element in this section
                    )}
                  </>
                )}
                {section === "Social Details" && (
                  <>
                    {Object.entries(contact?.socialMedia || {}).map(
                      ([key, value], index, array) =>
                        renderDetailItem(
                          key.charAt(0).toUpperCase() + key.slice(1),
                          value?.url,
                          index === array.length - 1 // No line for the last element
                        )
                    )}
                  </>
                )}
              </View>
            )}
            <View
              style={{
                borderBottomWidth: 1,
                borderBottomColor: "#ccc",
                marginVertical: 10,
              }}
            />
          </View>
        )
      )}
    </View>
  );
};

export default InfoContainerLayout3and4;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  scroll: { paddingBottom: 40 },
  profileContainer: { alignItems: "center" },
  profileImage: {
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height * 0.35,
  },
  info: {
    alignItems: "center",
    backgroundColor: "#00000080",
    position: "absolute",
    bottom: 0,
    width: "100%",
    paddingVertical: 10,
  },
  name: { color: "#fff" },
  subtitle: { color: "#ccc" },
  infoContainer: {
    marginHorizontal: 20,
    backgroundColor: colors.white,
    padding: 20,
    borderRadius: 15,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 10,
    marginVertical: 5,
  },
  sectionTitle: { fontWeight: "600", fontSize: 16 },
  arrow: { height: 16, width: 16 },
  sectionContent: { marginVertical: 8 },
  detailItem: {
    marginBottom: 8,
    flexDirection: "row",
    justifyContent: "space-between",
  },
  label: { fontSize: 14 },
  value: { color: colors.black, textTransform: "capitalize" },
  syncButton: {
    marginHorizontal: 20,
    marginTop: 20,
    backgroundColor: colors.primary,
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
  },
  syncText: { color: "#fff", fontWeight: "600", fontSize: 16 },
  optionText: {
    alignSelf: "left",
  },
  sortBoxOverlay: {
    position: "absolute",
    top: 80,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 150,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
});
