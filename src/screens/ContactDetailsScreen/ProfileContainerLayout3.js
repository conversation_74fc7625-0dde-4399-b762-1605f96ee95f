import {
  StyleSheet,
  TouchableOpacity,
  View,
  Dimensions,
  Image,
} from "react-native";
import commonStyles from "../../assets/commonStyles";
import icons from "../../assets/icons";
import colors from "../../assets/colors";
import SortOptionsBox from "../../components/SortOptionsBox";
import images from "../../assets/images";
import MyText from "../../components/MyText";
import Header from "../../components/Header";
import ProfilePicture from "../../components/ProfilePicture";

const ProfileContainerLayout3 = ({
  contact,
  navigation,
  isSortBoxVisible,
  setSortBoxVisible,
  handleSortOption,
  sortOptions,
}) => {
  return (
    <View style={styles.profileContainer}>
      <Header
        leftIcon={icons.backButton}
        title={"User Detail"}
        onPressLeft={() => navigation.goBack()}
        rightIcon1={icons.changeLayoutIcon}
        onPressRight={() => setSortBoxVisible((prev) => !prev)}
        // isAvatar={true}
        textCenter={true}
        noStatusBar={true}
        pb={50}
      />
      {isSortBoxVisible && (
        <SortOptionsBox
          options={sortOptions}
          onSelect={handleSortOption}
          style={[styles.sortBoxOverlay, { right: 40 }]}
          optionStyle={styles.sortBoxOption}
          optionTextStyle={styles.optionText}
        />
      )}
      <View style={styles.infoContainer}>
        <ProfilePicture
          uri={contact?.profile_image}
          customStyle={styles.profileImage}
          notEditable={true}
        />
        <View style={styles.info}>
          <MyText bold style={styles.name}>
            {contact?.firstName} {contact?.lastName}
          </MyText>

          <View style={styles.sectionHeader}>
            <View
              style={[
                { alignItems: "flex-start", flexDirection: "row", gap: 2 },
              ]}
            >
              <Image
                source={icons.callIcon}
                resizeMode="contain"
                style={styles.arrow}
              />
              <MyText style={[styles.subtitle]}>
                {contact?.phoneNumbers?.[0]?.number
                  ? `${
                      contact?.phoneNumbers?.[0]?.countryCode?.startsWith("+")
                        ? contact.phoneNumbers[0].countryCode
                        : `+${contact.phoneNumbers[0].countryCode || ""}`
                    } ${contact.phoneNumbers[0].number}`
                  : "No phone number available"}
              </MyText>
            </View>

            {/* <View
              style={[
                commonStyles.rowWithoutSpaceBetween,
                { alignItems: 'flex-start' },
              ]}
            >
            <Image source={icons.profilesIcon} resizeMode="contain" style={[styles.arrow, {tintColor: colors.primary}]} />
              <MyText style={styles.subtitle}>Profile:</MyText>
              <MyText style={[styles.subtitle, { color: colors.primary }]}>
                Friends
              </MyText>
            </View> */}
          </View>
        </View>
      </View>
    </View>
  );
};

export default ProfileContainerLayout3;

const screenHeight = Dimensions.get("window").height;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  scroll: { paddingBottom: 40 },
  profileContainer: {},
  profileImage: {
    width: Dimensions.get("window").width / 5,
    height: Dimensions.get("window").width / 5,
    borderRadius: 15,
  },
  info: {
    justifyContent: "space-evenly",
  },
  name: {
    color: colors.black,
    textTransform: "capitalize",
  },
  subtitle: { color: colors.black },
  infoContainer: {
    backgroundColor: "#EDEFFF",
    flexDirection: "row",
    margin: 20,
    borderRadius: 15,
    padding: 10,
    gap: 10,
  },
  sectionHeader: { gap: 2 },
  sectionTitle: { fontWeight: "600", fontSize: 16 },
  arrow: { height: 16, width: 16 },
  sectionContent: {},
  optionText: {
    alignSelf: "left",
  },
  sortBoxOverlay: {
    position: "absolute",
    top: 80,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 150,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
});
