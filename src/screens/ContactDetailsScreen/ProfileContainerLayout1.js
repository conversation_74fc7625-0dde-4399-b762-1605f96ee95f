import { StyleSheet, TouchableOpacity, View, Dimensions, Image } from "react-native";
import commonStyles from "../../assets/commonStyles";
import icons from "../../assets/icons";
import colors from "../../assets/colors";
import SortOptionsBox from "../../components/SortOptionsBox";
import images from "../../assets/images";
import MyText from "../../components/MyText";
import FastImage from "@d11/react-native-fast-image";

const ProfileContainerLayout1 = ({
    contact,
    navigation,
    isSortBoxVisible,
    setSortBoxVisible,
    handleSortOption,
    sortOptions,
    personal,
}) => {

  

    return ( 
          <View style={styles.profileContainer}>
          {/* back button */}
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            style={{
              position: "absolute",
              top: 40,
              left: 20,
              zIndex: 1,
            }}
          >
            <Image
              source={icons.backButton}
              style={commonStyles.header.midIcon}
            />
          </TouchableOpacity>
          {/* right icon exact same */}

          {!personal && <TouchableOpacity
            onPress={() =>
              setSortBoxVisible((prev) => !prev)
            }
            style={{
              position: "absolute",
              top: 40,
              right: 20,
              zIndex: 1,
            }}
          >
            <Image
              source={icons.changeLayoutIcon}
              style={commonStyles.header.midIcon}
            />
          </TouchableOpacity>}
              {isSortBoxVisible && (
                      <SortOptionsBox
                        options={sortOptions}
                        onSelect={handleSortOption}
                        style={[styles.sortBoxOverlay, { right: 40 }]}
                        optionStyle={styles.sortBoxOption}
                        optionTextStyle={styles.optionText}
                      />
                    )}

          <FastImage
            // source={{ uri: "https://www.ledr.com/colours/multi.htm", priority: FastImage.priority.high }}
            source={contact?.profile_image? { uri: contact?.profile_image, priority: FastImage.priority.high } : { uri: "https://ilyncdev.s3.us-east-1.amazonaws.com/uploads/profileImages/doc_1747910289357-87479643.png", priority: FastImage.priority.high }}
            style={styles.profileImage}
          />
          <View style={styles.info}>
            <MyText h5 style={styles.name}>
              {contact?.firstName} {contact?.middleName} {contact?.lastName}
            </MyText>
            {/* <MyText p style={styles.subtitle}>
              Profile: Friends
            </MyText> */}

            <TouchableOpacity
              onPress={() =>{
                if(personal){
                  navigation.navigate("ProfileCompletionScreen", {ownProfileData: contact, profileId: contact?.profile_management_id});
                } else {
                  navigation.navigate("EditContactScreen", { contact })
                }
              }
              }
              style={{
                flexDirection: "row",
                alignItems: "center",
                marginBottom: 10,
                // backgroundColor: "pink",
                position: "absolute",
                right: 20,
                top: 0,
                bottom: 0,
              }}
            >
              <Image
                source={icons.editButtonIcon}
                style={commonStyles.header.midIcon}
              />
            </TouchableOpacity>
          </View>
        </View>
     );
}
 
export default ProfileContainerLayout1;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  scroll: { paddingBottom: 40 },
  profileContainer: { alignItems: "center" },
  profileImage: {
    width: Dimensions.get("window").width,
    height: Dimensions.get("window").height * 0.35,
  },
  info: {
    alignItems: "center",
    backgroundColor: "#00000080",
    position: "absolute",
    bottom: 0,
    width: "100%",
    paddingVertical: 10,
  },
  name: { color: "#fff", textTransform: "capitalize" },
  subtitle: { color: "#ccc" },
  infoContainer: { padding: 20 },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    paddingVertical: 10,
    marginVertical: 5,
  },
  sectionTitle: { fontWeight: "600", fontSize: 16 },
  arrow: { height: 16, width: 16 },
  sectionContent: { marginVertical: 8 },
  detailItem: { marginBottom: 8 },
  label: { fontSize: 14 },
  value: { color: colors.black, textTransform: "capitalize" },
  syncButton: {
    marginHorizontal: 20,
    marginTop: 20,
    backgroundColor: colors.primary,
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
  },
  syncText: { color: "#fff", fontWeight: "600", fontSize: 16 },
    optionText: {
    alignSelf: "left",
  },
  sortBoxOverlay: {
    position: "absolute",
    top: 80,
    right: 10,
    backgroundColor: "#fff",
    borderRadius: 8,
    elevation: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.18,
    shadowRadius: 6,
    zIndex: 20000,
    paddingVertical: 8,
    minWidth: 150,
  },
  sortBoxOption: {
    paddingVertical: 4,
    paddingHorizontal: 6,
  },
});